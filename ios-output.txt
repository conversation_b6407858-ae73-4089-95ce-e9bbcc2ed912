2025-07-03 06:24:50,981 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-03 06:24:50,982 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-03 06:24:50,982 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-03 06:24:50,982 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-03 06:24:50,983 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-03 06:24:50,983 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-03 06:24:50,983 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-03 06:24:50,983 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-03 06:24:50,984 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-03 06:24:50,984 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-03 06:24:50,984 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-03 06:24:50,985 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-07-03 06:24:50,985 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-07-03 06:24:53,036 - __main__ - INFO - Existing processes terminated
2025-07-03 06:24:53,993 - utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
2025-07-03 06:24:53,994 - utils.global_values_db - INFO - Global values database initialized successfully
2025-07-03 06:24:53,994 - utils.global_values_db - INFO - Using global values from config.py
2025-07-03 06:24:53,994 - utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-07-03 06:24:53,997 - utils.global_values_db - INFO - Global value saved: Connection Retry Attempts=3 (type: int)
2025-07-03 06:24:53,997 - utils.global_values_db - INFO - Initialized default value: Connection Retry Attempts=3
2025-07-03 06:24:53,998 - utils.global_values_db - INFO - Global value saved: Connection Retry Delay=2 (type: int)
2025-07-03 06:24:53,998 - utils.global_values_db - INFO - Initialized default value: Connection Retry Delay=2
2025-07-03 06:24:53,999 - utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-07-03 06:24:53,999 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-03 06:24:54,030 - AppiumDeviceController - INFO - Successfully imported Airtest library.
